package com.semptian.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.DisplayName;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 雪花算法ID生成器测试类
 * 
 * <AUTHOR>
 * @date 2025/05/27
 */
class SnowflakeIdGeneratorTest {

    private SnowflakeIdGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new SnowflakeIdGenerator(1L, 1L);
    }

    @Test
    @DisplayName("测试基本ID生成功能")
    void testBasicIdGeneration() {
        long id1 = generator.nextId();
        long id2 = generator.nextId();
        
        // ID应该大于0
        assertTrue(id1 > 0);
        assertTrue(id2 > 0);
        
        // 连续生成的ID应该不同
        assertNotEquals(id1, id2);
        
        // 后生成的ID应该大于先生成的ID（保证有序性）
        assertTrue(id2 > id1);
        
        System.out.println("生成的ID1: " + id1);
        System.out.println("生成的ID2: " + id2);
        System.out.println("ID1解析: " + generator.parseId(id1));
        System.out.println("ID2解析: " + generator.parseId(id2));
    }

    @Test
    @DisplayName("测试ID唯一性")
    @RepeatedTest(5)
    void testIdUniqueness() {
        Set<Long> ids = new HashSet<>();
        int count = 10000;
        
        for (int i = 0; i < count; i++) {
            long id = generator.nextId();
            assertFalse(ids.contains(id), "发现重复ID: " + id);
            ids.add(id);
        }
        
        assertEquals(count, ids.size(), "生成的ID数量不匹配");
    }

    @Test
    @DisplayName("测试高并发环境下的ID唯一性")
    void testConcurrentIdGeneration() throws InterruptedException {
        int threadCount = 10;
        int idsPerThread = 1000;
        Set<Long> allIds = new HashSet<>();
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        AtomicInteger duplicateCount = new AtomicInteger(0);

        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    Set<Long> threadIds = new HashSet<>();
                    for (int j = 0; j < idsPerThread; j++) {
                        long id = generator.nextId();
                        threadIds.add(id);
                    }
                    
                    synchronized (allIds) {
                        for (Long id : threadIds) {
                            if (!allIds.add(id)) {
                                duplicateCount.incrementAndGet();
                                System.err.println("发现重复ID: " + id);
                            }
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        assertEquals(0, duplicateCount.get(), "发现重复ID");
        assertEquals(threadCount * idsPerThread, allIds.size(), "生成的ID总数不匹配");
        System.out.println("并发测试完成，生成了 " + allIds.size() + " 个唯一ID");
    }

    @Test
    @DisplayName("测试构造函数参数验证")
    void testConstructorValidation() {
        // 测试有效参数
        assertDoesNotThrow(() -> new SnowflakeIdGenerator(0L, 0L));
        assertDoesNotThrow(() -> new SnowflakeIdGenerator(31L, 31L));
        
        // 测试无效的机器ID
        assertThrows(IllegalArgumentException.class, () -> new SnowflakeIdGenerator(-1L, 1L));
        assertThrows(IllegalArgumentException.class, () -> new SnowflakeIdGenerator(32L, 1L));
        
        // 测试无效的数据中心ID
        assertThrows(IllegalArgumentException.class, () -> new SnowflakeIdGenerator(1L, -1L));
        assertThrows(IllegalArgumentException.class, () -> new SnowflakeIdGenerator(1L, 32L));
    }

    @Test
    @DisplayName("测试不同机器ID生成的ID不同")
    void testDifferentWorkerIds() {
        SnowflakeIdGenerator generator1 = new SnowflakeIdGenerator(1L, 1L);
        SnowflakeIdGenerator generator2 = new SnowflakeIdGenerator(2L, 1L);
        
        // 在相近时间生成ID，应该不同
        long id1 = generator1.nextId();
        long id2 = generator2.nextId();
        
        assertNotEquals(id1, id2);
        
        System.out.println("机器1生成的ID: " + generator1.parseId(id1));
        System.out.println("机器2生成的ID: " + generator2.parseId(id2));
    }

    @Test
    @DisplayName("测试ID解析功能")
    void testIdParsing() {
        long id = generator.nextId();
        String parsed = generator.parseId(id);
        
        assertNotNull(parsed);
        assertTrue(parsed.contains("数据中心ID: 1"));
        assertTrue(parsed.contains("机器ID: 1"));
        
        System.out.println("ID解析结果: " + parsed);
    }

    @Test
    @DisplayName("测试获取机器ID和数据中心ID")
    void testGetterMethods() {
        assertEquals(1L, generator.getWorkerId());
        assertEquals(1L, generator.getDatacenterId());
    }

    @Test
    @DisplayName("测试ID的时间有序性")
    void testIdTimeOrdering() throws InterruptedException {
        long id1 = generator.nextId();
        Thread.sleep(1); // 确保时间戳不同
        long id2 = generator.nextId();
        
        // 后生成的ID应该大于先生成的ID
        assertTrue(id2 > id1, "ID应该按时间有序");
    }

    @Test
    @DisplayName("测试同一毫秒内的序列号递增")
    void testSequenceIncrement() {
        // 快速生成多个ID，应该在同一毫秒内
        long id1 = generator.nextId();
        long id2 = generator.nextId();
        long id3 = generator.nextId();
        
        // 验证ID递增
        assertTrue(id2 > id1);
        assertTrue(id3 > id2);
        
        System.out.println("同毫秒内ID序列:");
        System.out.println(generator.parseId(id1));
        System.out.println(generator.parseId(id2));
        System.out.println(generator.parseId(id3));
    }
}
