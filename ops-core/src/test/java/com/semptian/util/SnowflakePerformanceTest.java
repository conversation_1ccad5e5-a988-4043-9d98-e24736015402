package com.semptian.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 雪花算法性能测试类
 * 
 * <AUTHOR>
 * @date 2025/05/27
 */
class SnowflakePerformanceTest {

    @Test
    @DisplayName("单线程性能测试")
    void testSingleThreadPerformance() {
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1L, 1L);
        int count = 1000000; // 100万次
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < count; i++) {
            generator.nextId();
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        double qps = (double) count / duration * 1000;
        
        System.out.println("=== 单线程性能测试结果 ===");
        System.out.println("生成ID数量: " + count);
        System.out.println("耗时: " + duration + "ms");
        System.out.println("QPS: " + String.format("%.2f", qps));
        System.out.println("平均每个ID耗时: " + String.format("%.4f", (double) duration / count) + "ms");
    }

    @Test
    @DisplayName("多线程性能测试")
    void testMultiThreadPerformance() throws InterruptedException {
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1L, 1L);
        int threadCount = 10;
        int idsPerThread = 100000; // 每线程10万次
        int totalCount = threadCount * idsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < idsPerThread; j++) {
                        generator.nextId();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        double qps = (double) totalCount / duration * 1000;
        
        System.out.println("=== 多线程性能测试结果 ===");
        System.out.println("线程数: " + threadCount);
        System.out.println("总生成ID数量: " + totalCount);
        System.out.println("耗时: " + duration + "ms");
        System.out.println("QPS: " + String.format("%.2f", qps));
        System.out.println("平均每个ID耗时: " + String.format("%.4f", (double) duration / totalCount) + "ms");
    }

    @Test
    @DisplayName("内存使用测试")
    void testMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        
        // 强制垃圾回收
        System.gc();
        long beforeMemory = runtime.totalMemory() - runtime.freeMemory();
        
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1L, 1L);
        int count = 1000000;
        
        // 生成ID但不保存，测试生成过程的内存使用
        for (int i = 0; i < count; i++) {
            generator.nextId();
        }
        
        System.gc();
        long afterMemory = runtime.totalMemory() - runtime.freeMemory();
        
        System.out.println("=== 内存使用测试结果 ===");
        System.out.println("生成ID数量: " + count);
        System.out.println("测试前内存使用: " + beforeMemory / 1024 / 1024 + "MB");
        System.out.println("测试后内存使用: " + afterMemory / 1024 / 1024 + "MB");
        System.out.println("内存增长: " + (afterMemory - beforeMemory) / 1024 / 1024 + "MB");
    }

    @Test
    @DisplayName("压力测试 - 极限并发")
    void testStressTest() throws InterruptedException {
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1L, 1L);
        int threadCount = 50; // 50个线程
        int idsPerThread = 50000; // 每线程5万次
        int totalCount = threadCount * idsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    long threadStartTime = System.currentTimeMillis();
                    for (int j = 0; j < idsPerThread; j++) {
                        generator.nextId();
                    }
                    long threadEndTime = System.currentTimeMillis();
                    System.out.println("线程" + threadId + "完成，耗时: " + (threadEndTime - threadStartTime) + "ms");
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        double qps = (double) totalCount / duration * 1000;
        
        System.out.println("=== 压力测试结果 ===");
        System.out.println("线程数: " + threadCount);
        System.out.println("总生成ID数量: " + totalCount);
        System.out.println("总耗时: " + duration + "ms");
        System.out.println("总QPS: " + String.format("%.2f", qps));
        System.out.println("平均每线程QPS: " + String.format("%.2f", qps / threadCount));
    }

    @Test
    @DisplayName("ID分布均匀性测试")
    void testIdDistribution() {
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1L, 1L);
        int count = 10000;
        long[] ids = new long[count];
        
        // 生成ID
        for (int i = 0; i < count; i++) {
            ids[i] = generator.nextId();
        }
        
        // 分析ID分布
        long min = ids[0];
        long max = ids[0];
        long sum = 0;
        
        for (long id : ids) {
            min = Math.min(min, id);
            max = Math.max(max, id);
            sum += id;
        }
        
        double average = (double) sum / count;
        
        // 计算方差
        double variance = 0;
        for (long id : ids) {
            variance += Math.pow(id - average, 2);
        }
        variance /= count;
        double stdDev = Math.sqrt(variance);
        
        System.out.println("=== ID分布均匀性测试结果 ===");
        System.out.println("生成ID数量: " + count);
        System.out.println("最小ID: " + min);
        System.out.println("最大ID: " + max);
        System.out.println("平均值: " + String.format("%.2f", average));
        System.out.println("标准差: " + String.format("%.2f", stdDev));
        System.out.println("ID范围: " + (max - min));
        
        // 检查ID是否严格递增
        boolean isStrictlyIncreasing = true;
        for (int i = 1; i < count; i++) {
            if (ids[i] <= ids[i-1]) {
                isStrictlyIncreasing = false;
                break;
            }
        }
        System.out.println("ID严格递增: " + isStrictlyIncreasing);
    }
}
